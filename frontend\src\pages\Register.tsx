// frontend/src/pages/Register.tsx
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import { Input } from '../components/ui/Input';
import { Button } from '../components/ui/Button';
import { Icon } from '../components/ui/Icon';
import { AuthWrapper } from '../components/AuthWrapper';
import { OTPInput } from '../components/OTPInput';
import { useAuth } from '../contexts/AuthContext';
import { useOTP } from '../hooks/useOTP';
import { signupSchema, otpSchema } from '../utils/validationSchemas';

interface RegisterForm {
  email: string;
  fullName: string;
  password: string;
  confirmPassword: string;
}

interface OTPForm {
  otp: string;
}

export const Register: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [step, setStep] = useState<'signup' | 'otp'>('signup');
  const [userEmail, setUserEmail] = useState('');
  const [userName, setUserName] = useState('');
  
  const { register, loading } = useAuth();
  const { otpState, requestOTP, verifyOTP, resetOTPState } = useOTP('signup');
  const navigate = useNavigate();

  const handleSignupSubmit = async (values: RegisterForm) => {
    try {
      // Split full name into first and last name for backend compatibility
      const nameParts = values.fullName.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Request OTP for signup
      await requestOTP({
        email: values.email,
        fullName: values.fullName,
        purpose: 'signup'
      });

      setUserEmail(values.email);
      setUserName(values.fullName);
      setStep('otp');
    } catch (err: any) {
      // Error is handled by the OTP hook
      console.error('Signup OTP request failed:', err);
    }
  };

  const handleOTPSubmit = async (values: OTPForm) => {
    try {
      const result = await verifyOTP({
        email: userEmail,
        otp: values.otp,
        purpose: 'signup',
        fullName: userName
      });

      if (result.success) {
        navigate('/dashboard');
      }
    } catch (err: any) {
      // Error is handled by the OTP hook
      console.error('OTP verification failed:', err);
    }
  };

  const handleBackToSignup = () => {
    setStep('signup');
    resetOTPState();
  };

  const handleResendOTP = async () => {
    if (otpState.canResend) {
      try {
        await requestOTP({
          email: userEmail,
          fullName: userName,
          purpose: 'signup'
        });
      } catch (err) {
        console.error('Resend OTP failed:', err);
      }
    }
  };

  const handleGoogleSignup = () => {
    // TODO: Implement Google OAuth signup
    console.log('Google signup clicked');
  };

  return (
    <AuthWrapper
      title={step === 'signup' ? "Create your account" : "Verify your email"}
      subtitle={step === 'signup' ? "Join us today and start connecting" : `We've sent a 6-digit code to ${userEmail}`}
    >
      {/* Toggle Buttons */}
      <div className="flex bg-gray-100 rounded-lg p-1 mb-6">
        <Link
          to="/login"
          className="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 flex items-center justify-center"
        >
          <Icon name="mail" size={16} className="inline mr-2" />
          Login
        </Link>
        <button
          type="button"
          className="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors bg-white text-gray-900 shadow-sm"
        >
          <Icon name="user-plus" size={16} className="inline mr-2" />
          Signup
        </button>
      </div>

      {step === 'signup' ? (
        <Formik
          initialValues={{
            email: '',
            fullName: '',
            password: '',
            confirmPassword: '',
          }}
          validationSchema={signupSchema}
          onSubmit={handleSignupSubmit}
        >
          {({ values, errors, touched, isSubmitting, setFieldValue }) => (
            <Form className="space-y-3">
              {/* Email Field */}
              <div>
                <Field name="email">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Email address"
                      type="email"
                      placeholder="Enter your email address"
                      className="w-full"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="email" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Full Name Field */}
              <div>
                <Field name="fullName">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Full name"
                      type="text"
                      placeholder="Enter your full name"
                      className="w-full"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="fullName" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Password Field */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <Field name="password">
                    {({ field, meta }: any) => (
                      <Input
                        {...field}
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Create a password"
                        className="w-full pr-10"
                        error={meta.touched && meta.error}
                      />
                    )}
                  </Field>
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <Icon name={showPassword ? 'eyeOff' : 'eye'} size={16} />
                  </button>
                </div>
                <ErrorMessage name="password" component="div" className="text-red-600 text-sm mt-1" />
                <p className="text-xs text-gray-500 mt-1">
                  Must contain uppercase, lowercase, and number
                </p>
              </div>

              {/* Confirm Password Field */}
              <div>
                <Field name="confirmPassword">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Confirm password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Confirm your password"
                      className="w-full"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="confirmPassword" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Error Message */}
              {otpState.error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <Icon name="alert" className="text-red-400" size={16} />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Registration Error
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{otpState.error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div>
                <Button
                  type="submit"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white py-3 rounded-lg font-medium"
                  loading={otpState.isLoading || isSubmitting}
                  disabled={otpState.isLoading || isSubmitting}
                >
                  Continue with Email Verification
                </Button>
              </div>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">OR</span>
                </div>
              </div>

              {/* Google SSO Button */}
              <div className="mt-6 space-y-3">
                <button
                  type="button"
                  onClick={handleGoogleSignup}
                  className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-gray-800 text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors"
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  <span className="ml-2">Continue with Google</span>
                </button>
              </div>
            </Form>
          )}
        </Formik>
      ) : (
        <Formik
          initialValues={{ otp: '' }}
          validationSchema={otpSchema}
          onSubmit={handleOTPSubmit}
        >
          {({ values, setFieldValue, isSubmitting }) => (
            <Form className="space-y-6">
              {/* OTP Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4 text-center">
                  Enter verification code
                </label>
                <OTPInput
                  value={values.otp}
                  onChange={(value) => setFieldValue('otp', value)}
                  disabled={otpState.isLoading || isSubmitting}
                  error={!!otpState.error}
                />
                <ErrorMessage name="otp" component="div" className="text-red-600 text-sm mt-2 text-center" />
              </div>

              {/* Error Message */}
              {otpState.error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <Icon name="alert" className="text-red-400" size={16} />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Verification Error
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{otpState.error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Verify Button */}
              <div>
                <Button
                  type="submit"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white py-3 rounded-lg font-medium"
                  loading={otpState.isLoading || isSubmitting}
                  disabled={otpState.isLoading || isSubmitting || values.otp.length !== 6}
                >
                  Verify & Create Account
                </Button>
              </div>

              {/* Resend OTP */}
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Didn't receive the code?
                </p>
                {otpState.countdown > 0 ? (
                  <p className="text-sm text-gray-500">
                    Resend in {otpState.countdown}s
                  </p>
                ) : (
                  <button
                    type="button"
                    onClick={handleResendOTP}
                    disabled={!otpState.canResend || otpState.isLoading}
                    className="text-sm font-medium text-blue-600 hover:text-blue-500 disabled:text-gray-400"
                  >
                    Resend code
                  </button>
                )}
              </div>

              {/* Back to signup */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={handleBackToSignup}
                  className="text-sm font-medium text-gray-600 hover:text-gray-900"
                >
                  ← Back to signup
                </button>
              </div>
            </Form>
          )}
        </Formik>
      )}

      {/* Footer */}
      <div className="text-center">
        <p className="text-sm text-gray-600">
          Already have an account?{' '}
          <Link
            to="/login"
            className="font-medium text-blue-600 hover:text-blue-500"
          >
            Sign in
          </Link>
        </p>
      </div>
    </AuthWrapper>
  );
};

export default Register;
