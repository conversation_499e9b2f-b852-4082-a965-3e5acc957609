// frontend/src/pages/Register.tsx
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import { Input } from '../components/ui/Input';
import { Button } from '../components/ui/Button';
import { Icon } from '../components/ui/Icon';
import { useRegisterMutation } from '../services/authApi';
import { signupSchema } from '../utils/validationSchemas';

interface RegisterForm {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
}

export const Register: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [registerMutation, { isLoading }] = useRegisterMutation();
  const navigate = useNavigate();

  const handleRegisterSubmit = async (values: RegisterForm) => {
    try {
      // Prepare data for backend API
      const registerData = {
        username: values.username,
        email: values.email,
        password: values.password,
        first_name: values.firstName,
        last_name: values.lastName,
      };

      const result = await registerMutation(registerData).unwrap();

      if (result.success && result.data) {
        // Store tokens and user data
        localStorage.setItem('token', result.data.tokens.access);
        localStorage.setItem('refreshToken', result.data.tokens.refresh);

        // Navigate to chat
        navigate('/chat');
      }
    } catch (error: any) {
      // Error handling is managed by Formik
      throw new Error(error?.data?.error || 'Registration failed');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
            <Icon name="message" className="text-white" size={24} />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Create your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Already have an account?{' '}
          <Link
            to="/login"
            className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
          >
            Sign in
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white/20 backdrop-blur-lg border border-white/30 rounded-2xl shadow-xl py-8 px-4 sm:px-10">
          <Formik
            initialValues={{
              username: '',
              email: '',
              firstName: '',
              lastName: '',
              password: '',
              confirmPassword: '',
            }}
            validationSchema={signupSchema}
            onSubmit={handleRegisterSubmit}
          >
            {({ errors, touched, isSubmitting }) => (
            <Form className="space-y-4">
              {/* Email Field */}
              <div>
                <Field name="email">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Email address"
                      type="email"
                      placeholder="Enter your email address"
                      className="w-full bg-white/50 border-white/30 focus:border-blue-400 focus:ring-blue-400/50 rounded-lg"
                      data-testid="email-input"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="email" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Username Field */}
              <div>
                <Field name="username">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Username"
                      type="text"
                      placeholder="Choose a username"
                      className="w-full bg-white/50 border-white/30 focus:border-blue-400 focus:ring-blue-400/50 rounded-lg"
                      data-testid="username-input"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="username" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* First Name and Last Name Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Field name="firstName">
                    {({ field, meta }: any) => (
                      <Input
                        {...field}
                        label="First name"
                        type="text"
                        placeholder="First name"
                        className="w-full bg-white/50 border-white/30 focus:border-blue-400 focus:ring-blue-400/50 rounded-lg"
                        data-testid="firstName-input"
                        error={meta.touched && meta.error}
                      />
                    )}
                  </Field>
                  <ErrorMessage name="firstName" component="div" className="text-red-600 text-sm mt-1" />
                </div>
                <div>
                  <Field name="lastName">
                    {({ field, meta }: any) => (
                      <Input
                        {...field}
                        label="Last name"
                        type="text"
                        placeholder="Last name"
                        className="w-full bg-white/50 border-white/30 focus:border-blue-400 focus:ring-blue-400/50 rounded-lg"
                        data-testid="lastName-input"
                        error={meta.touched && meta.error}
                      />
                    )}
                  </Field>
                  <ErrorMessage name="lastName" component="div" className="text-red-600 text-sm mt-1" />
                </div>
              </div>

              {/* Password Field */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Password
                  </label>
                </div>
                <div className="relative">
                  <Field name="password">
                    {({ field, meta }: any) => (
                      <Input
                        {...field}
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Create a password"
                        className="w-full bg-white/50 border-white/30 focus:border-blue-400 focus:ring-blue-400/50 rounded-lg pr-10"
                        data-testid="password-input"
                        error={meta.touched && meta.error}
                      />
                    )}
                  </Field>
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                    data-testid="show-password-button"
                  >
                    <Icon name={showPassword ? 'eyeOff' : 'eye'} size={16} />
                  </button>
                </div>
                <ErrorMessage name="password" component="div" className="text-red-600 text-sm mt-1" />
                <p className="text-xs text-gray-500 mt-1">
                  Must contain uppercase, lowercase, and number
                </p>
              </div>

              {/* Confirm Password Field */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Confirm Password
                  </label>
                </div>
                <div className="relative">
                  <Field name="confirmPassword">
                    {({ field, meta }: any) => (
                      <Input
                        {...field}
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder="Confirm your password"
                        className="w-full bg-white/50 border-white/30 focus:border-blue-400 focus:ring-blue-400/50 rounded-lg pr-10"
                        data-testid="confirmPassword-input"
                        error={meta.touched && meta.error}
                      />
                    )}
                  </Field>
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    data-testid="show-confirm-password-button"
                  >
                    <Icon name={showConfirmPassword ? 'eyeOff' : 'eye'} size={16} />
                  </button>
                </div>
                <ErrorMessage name="confirmPassword" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Submit Button */}
              <div>
                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                  loading={isLoading || isSubmitting}
                  disabled={isLoading || isSubmitting}
                  data-testid="register-button"
                >
                  Create Account
                </Button>
              </div>

              {/* Divider */}
              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-white/30" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white/20 text-gray-600">Or continue with</span>
                  </div>
                </div>
              </div>

              {/* Social Login Buttons */}
              <div className="mt-6 space-y-3">
                <button
                  type="button"
                  className="w-full inline-flex justify-center py-3 px-4 border border-white/30 rounded-xl shadow-sm bg-white/30 backdrop-blur-sm text-gray-700 text-sm font-medium hover:bg-white/40 transition-all duration-200"
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  <span className="ml-2">Continue with Google</span>
                </button>
              </div>
            </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default Register;
