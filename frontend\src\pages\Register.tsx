// frontend/src/pages/Register.tsx
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import { Input } from '../components/ui/Input';
import { Button } from '../components/ui/Button';
import { Icon } from '../components/ui/Icon';
import { useRegisterMutation } from '../services/authApi';
import { signupSchema } from '../utils/validationSchemas';

interface RegisterForm {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
}

export const Register: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [registerMutation, { isLoading }] = useRegisterMutation();
  const navigate = useNavigate();

  const handleRegisterSubmit = async (values: RegisterForm) => {
    try {
      // Prepare data for backend API
      const registerData = {
        username: values.username,
        email: values.email,
        password: values.password,
        first_name: values.firstName,
        last_name: values.lastName,
      };

      const result = await registerMutation(registerData).unwrap();

      if (result.success && result.data) {
        // Store tokens and user data
        localStorage.setItem('token', result.data.tokens.access);
        localStorage.setItem('refreshToken', result.data.tokens.refresh);

        // Navigate to chat
        navigate('/chat');
      }
    } catch (error: any) {
      // Error handling is managed by Formik
      throw new Error(error?.data?.error || 'Registration failed');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
            <Icon name="message" className="text-white" size={24} />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Create your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Already have an account?{' '}
          <Link
            to="/login"
            className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
          >
            Sign in
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white/20 backdrop-blur-lg border border-white/30 rounded-2xl shadow-xl py-8 px-4 sm:px-10">
          <Formik
            initialValues={{
              username: '',
              email: '',
              firstName: '',
              lastName: '',
              password: '',
              confirmPassword: '',
            }}
            validationSchema={signupSchema}
            onSubmit={handleRegisterSubmit}
          >
            {({ errors, touched, isSubmitting }) => (
            <Form className="space-y-3">
              {/* Email Field */}
              <div>
                <Field name="email">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Email address"
                      type="email"
                      placeholder="Enter your email address"
                      className="w-full"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="email" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Full Name Field */}
              <div>
                <Field name="fullName">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Full name"
                      type="text"
                      placeholder="Enter your full name"
                      className="w-full"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="fullName" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Password Field */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <Field name="password">
                    {({ field, meta }: any) => (
                      <Input
                        {...field}
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Create a password"
                        className="w-full pr-10"
                        error={meta.touched && meta.error}
                      />
                    )}
                  </Field>
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <Icon name={showPassword ? 'eyeOff' : 'eye'} size={16} />
                  </button>
                </div>
                <ErrorMessage name="password" component="div" className="text-red-600 text-sm mt-1" />
                <p className="text-xs text-gray-500 mt-1">
                  Must contain uppercase, lowercase, and number
                </p>
              </div>

              {/* Confirm Password Field */}
              <div>
                <Field name="confirmPassword">
                  {({ field, meta }: any) => (
                    <Input
                      {...field}
                      label="Confirm password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Confirm your password"
                      className="w-full"
                      error={meta.touched && meta.error}
                    />
                  )}
                </Field>
                <ErrorMessage name="confirmPassword" component="div" className="text-red-600 text-sm mt-1" />
              </div>

              {/* Error Message */}
              {otpState.error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <Icon name="alert" className="text-red-400" size={16} />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Registration Error
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{otpState.error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div>
                <Button
                  type="submit"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white py-3 rounded-lg font-medium"
                  loading={otpState.isLoading || isSubmitting}
                  disabled={otpState.isLoading || isSubmitting}
                >
                  Continue with Email Verification
                </Button>
              </div>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">OR</span>
                </div>
              </div>

              {/* Google SSO Button */}
              <div className="mt-6 space-y-3">
                <button
                  type="button"
                  onClick={handleGoogleSignup}
                  className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-gray-800 text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors"
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  <span className="ml-2">Continue with Google</span>
                </button>
              </div>
            </Form>
          )}
        </Formik>
      ) : (
        <Formik
          initialValues={{ otp: '' }}
          validationSchema={otpSchema}
          onSubmit={handleOTPSubmit}
        >
          {({ values, setFieldValue, isSubmitting }) => (
            <Form className="space-y-6">
              {/* OTP Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4 text-center">
                  Enter verification code
                </label>
                <OTPInput
                  value={values.otp}
                  onChange={(value) => setFieldValue('otp', value)}
                  disabled={otpState.isLoading || isSubmitting}
                  error={!!otpState.error}
                />
                <ErrorMessage name="otp" component="div" className="text-red-600 text-sm mt-2 text-center" />
              </div>

              {/* Error Message */}
              {otpState.error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <Icon name="alert" className="text-red-400" size={16} />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Verification Error
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{otpState.error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Verify Button */}
              <div>
                <Button
                  type="submit"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white py-3 rounded-lg font-medium"
                  loading={otpState.isLoading || isSubmitting}
                  disabled={otpState.isLoading || isSubmitting || values.otp.length !== 6}
                >
                  Verify & Create Account
                </Button>
              </div>

              {/* Resend OTP */}
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Didn't receive the code?
                </p>
                {otpState.countdown > 0 ? (
                  <p className="text-sm text-gray-500">
                    Resend in {otpState.countdown}s
                  </p>
                ) : (
                  <button
                    type="button"
                    onClick={handleResendOTP}
                    disabled={!otpState.canResend || otpState.isLoading}
                    className="text-sm font-medium text-blue-600 hover:text-blue-500 disabled:text-gray-400"
                  >
                    Resend code
                  </button>
                )}
              </div>

              {/* Back to signup */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={handleBackToSignup}
                  className="text-sm font-medium text-gray-600 hover:text-gray-900"
                >
                  ← Back to signup
                </button>
              </div>
            </Form>
          )}
        </Formik>
      )}

      {/* Footer */}
      <div className="text-center">
        <p className="text-sm text-gray-600">
          Already have an account?{' '}
          <Link
            to="/login"
            className="font-medium text-blue-600 hover:text-blue-500"
          >
            Sign in
          </Link>
        </p>
      </div>
    </AuthWrapper>
  );
};

export default Register;
