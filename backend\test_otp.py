#!/usr/bin/env python
"""
Test script for OTP authentication endpoints
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000/api/auth"

def test_signup_request_otp():
    """Test signup OTP request endpoint"""
    url = f"{BASE_URL}/signup/request-otp/"
    data = {
        "email": "<EMAIL>",
        "purpose": "signup"
    }
    
    print(f"Testing: {url}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_login_request_otp():
    """Test login OTP request endpoint"""
    url = f"{BASE_URL}/login/request-otp/"
    data = {
        "email": "<EMAIL>",
        "purpose": "login"
    }
    
    print(f"\nTesting: {url}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    print("=== Testing OTP Authentication Endpoints ===")
    
    # Test signup OTP request
    signup_response = test_signup_request_otp()
    
    # Test login OTP request
    login_response = test_login_request_otp()
    
    print("\n=== Test Summary ===")
    if signup_response:
        print(f"Signup OTP Request: {signup_response.status_code}")
    if login_response:
        print(f"Login OTP Request: {login_response.status_code}")