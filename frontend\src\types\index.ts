// frontend/src/types/index.ts
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  isVerified: boolean;
  lastSeen: string; // Store as ISO string for Redux serialization
  createdAt: string; // Store as ISO string for Redux serialization
}

// Backend API response format (now camelCase due to alias_generator=to_camel)
export interface BackendUser {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  isVerified: boolean;
  lastSeen: string;
  createdAt: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

// Backend API response format
export interface BackendAuthResponse {
  user: BackendUser;
  tokens: AuthTokens;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

// RTK Query specific types
export interface BaseQueryError {
  status: number;
  data: {
    success: false;
    error: string;
    timestamp: string;
  };
}

// API Request/Response types are now in api.ts

export interface RegisterRequest {
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  password: string;
}

// OTP-related types
export interface OTPRequest {
  email: string;
  purpose: 'signup' | 'login' | 'password_reset';
}

export interface OTPVerifyRequest {
  email: string;
  otpCode: string;
  name?: string; // Required for signup, optional for login
}

export interface OTPResponse {
  success: boolean;
  message: string;
  email?: string;
  expiresInMinutes?: number;
}

export interface OTPVerifyResponse {
  user: User;
  tokens: AuthTokens;
}

export interface SearchUsersRequest {
  q: string;
}

export interface SearchUsersResponse {
  success: boolean;
  results: Array<{
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    full_name: string;
    profile_picture?: string;
  }>;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    results: T[];
    count: number;
    next?: string;
    previous?: string;
  };
}

// RTK Query hook return types
export interface QueryResult<T> {
  data?: T;
  error?: any;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isFetching: boolean;
  refetch: () => void;
}

export interface MutationResult<T> {
  data?: T;
  error?: any;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  reset: () => void;
}

// Socket event types
export interface SocketMessage {
  id: string;
  conversationId: string;
  sender: {
    id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
  content: string;
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  createdAt: string;
  updatedAt: string;
}

export interface SocketConversation {
  id: string;
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: Array<{
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  }>;
  lastMessage?: {
    id: string;
    content: string;
    sender: {
      username: string;
    };
    createdAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface TypingEvent {
  conversationId: string;
  userId: string;
  username: string;
  isTyping: boolean;
}

export interface UserStatusEvent {
  userId: string;
  status: 'online' | 'offline';
  lastSeen?: string;
}

// API Error types
export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiErrorResponse {
  success: false;
  error: string;
  details?: ValidationError[];
  timestamp: string;
}

// Cache tag types for RTK Query
export type CacheTag =
  | { type: 'User'; id?: string }
  | { type: 'Conversation'; id?: string | 'LIST' }
  | { type: 'Message'; id?: string | 'LIST' }
  | { type: 'Auth'; id?: string }
  | { type: 'Encryption'; id?: string };

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Re-export encryption types
export * from './encryption';
