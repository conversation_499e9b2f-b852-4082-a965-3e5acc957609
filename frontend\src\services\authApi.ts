// frontend/src/services/authApi.ts
import { api } from './api';
import type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  BackendAuthResponse,
  User,
  BackendUser,
  ApiResponse,
  OTPRequest,
  OTPVerifyRequest,
  OTPResponse,
  OTPVerifyResponse
} from '../types';
import { transformBackendUser, storeUserData, clearUserData } from '../utils/auth';

export const authApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // OTP Endpoints
    requestSignupOTP: builder.mutation<OTPResponse, Pick<OTPRequest, 'email'>>({
      query: (data) => ({
        url: '/auth/signup/request-otp/',
        method: 'POST',
        body: { ...data, purpose: 'signup' },
      }),
      transformResponse: (response: any) => {
        return {
          success: true,
          message: response.message,
          email: response.email,
        };
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: {
          success: false,
          error: response.data?.error || 'Failed to send OTP',
          timestamp: new Date().toISOString()
        }
      }),
    }),

    verifySignupOTP: builder.mutation<ApiResponse<AuthResponse>, OTPVerifyRequest>({
      query: (data) => ({
        url: '/auth/signup/verify-otp/',
        method: 'POST',
        body: {
          email: data.email,
          otp_code: data.otpCode,
          name: data.name,
        },
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<BackendAuthResponse>) => {
        // Transform backend user data to frontend format
        if (response.success && response.data) {
          const transformedUser = transformBackendUser(response.data.user);
          storeUserData(transformedUser);

          return {
            ...response,
            data: {
              user: transformedUser,
              tokens: response.data.tokens
            }
          } as ApiResponse<AuthResponse>;
        }
        return response as unknown as ApiResponse<AuthResponse>;
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: {
          success: false,
          error: response.data?.error || 'OTP verification failed',
          timestamp: new Date().toISOString()
        }
      }),
    }),

    requestLoginOTP: builder.mutation<OTPResponse, Pick<OTPRequest, 'email'>>({
      query: (data) => ({
        url: '/auth/login/request-otp/',
        method: 'POST',
        body: { ...data, purpose: 'login' },
      }),
      transformResponse: (response: any) => {
        return {
          success: true,
          message: response.message,
          email: response.email,
        };
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: {
          success: false,
          error: response.data?.error || 'Failed to send OTP',
          timestamp: new Date().toISOString()
        }
      }),
    }),

    verifyLoginOTP: builder.mutation<ApiResponse<AuthResponse>, Omit<OTPVerifyRequest, 'name'>>({
      query: (data) => ({
        url: '/auth/login/verify-otp/',
        method: 'POST',
        body: {
          email: data.email,
          otp_code: data.otpCode,
        },
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<BackendAuthResponse>) => {
        // Transform backend user data to frontend format
        if (response.success && response.data) {
          const transformedUser = transformBackendUser(response.data.user);
          storeUserData(transformedUser);

          return {
            ...response,
            data: {
              user: transformedUser,
              tokens: response.data.tokens
            }
          } as ApiResponse<AuthResponse>;
        }
        return response as unknown as ApiResponse<AuthResponse>;
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: {
          success: false,
          error: response.data?.error || 'OTP verification failed',
          timestamp: new Date().toISOString()
        }
      }),
    }),

    login: builder.mutation<ApiResponse<AuthResponse>, LoginRequest>({
      query: (credentials) => ({
        url: '/auth/login/',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<BackendAuthResponse>) => {
        // Store tokens and user data in localStorage on successful login
        if (response.success && response.data) {
          localStorage.setItem('token', response.data.tokens.access);
          localStorage.setItem('refreshToken', response.data.tokens.refresh);

          // Transform and store user data
          const transformedUser = transformBackendUser(response.data.user);
          storeUserData(transformedUser);

          // Return transformed response
          return {
            ...response,
            data: {
              user: transformedUser,
              tokens: response.data.tokens
            }
          } as ApiResponse<AuthResponse>;
        }
        return response as unknown as ApiResponse<AuthResponse>;
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || {
            success: false,
            error: 'Login failed',
            timestamp: new Date().toISOString()
          }
        };
      },
    }),

    register: builder.mutation<ApiResponse<AuthResponse>, RegisterRequest>({
      query: (userData) => ({
        url: '/auth/register/',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<BackendAuthResponse>) => {
        // Store tokens and user data in localStorage on successful registration
        if (response.success && response.data) {
          localStorage.setItem('token', response.data.tokens.access);
          localStorage.setItem('refreshToken', response.data.tokens.refresh);

          // Transform and store user data
          const transformedUser = transformBackendUser(response.data.user);
          storeUserData(transformedUser);

          // Return transformed response
          return {
            ...response,
            data: {
              user: transformedUser,
              tokens: response.data.tokens
            }
          } as ApiResponse<AuthResponse>;
        }
        return response as unknown as ApiResponse<AuthResponse>;
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || {
            success: false,
            error: 'Registration failed',
            timestamp: new Date().toISOString()
          }
        };
      },
    }),

    // Note: Token refresh and logout are handled client-side since the backend
    // doesn't have explicit endpoints for these operations
    logout: builder.mutation<void, void>({
      queryFn: () => ({ data: undefined }),
      invalidatesTags: ['Auth', 'User', 'Conversation', 'Message'],
      onQueryStarted: async (_, { dispatch }) => {
        // Clear tokens, user data, and cached data
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        clearUserData();

        // Clear all cached data
        dispatch(api.util.resetApiState());
      },
    }),

    // Get current user profile (useful for checking auth status)
    getCurrentUser: builder.query<ApiResponse<{ user: User }>, void>({
      query: () => '/auth/profile/',
      providesTags: ['Auth', 'User'],
      transformResponse: (response: ApiResponse<BackendUser>) => {
        // Transform backend user data to frontend format
        if (response.success && response.data) {
          const transformedUser = transformBackendUser(response.data);
          storeUserData(transformedUser);

          return {
            ...response,
            data: { user: transformedUser }
          } as ApiResponse<{ user: User }>;
        }
        return response as unknown as ApiResponse<{ user: User }>;
      },
      transformErrorResponse: (response: any) => {
        // If user is not authenticated, clear tokens and user data
        if (response.status === 401) {
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          clearUserData();
        }
        return {
          status: response.status,
          data: response.data || {
            success: false,
            error: 'Failed to get user info',
            timestamp: new Date().toISOString()
          }
        };
      },
    }),
  }),
  overrideExisting: false,
});

export const {
  useRequestSignupOTPMutation,
  useVerifySignupOTPMutation,
  useRequestLoginOTPMutation,
  useVerifyLoginOTPMutation,
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useLazyGetCurrentUserQuery,
} = authApi;
